<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ScamShield - Demo</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 400px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .header h1 {
            margin: 0;
            font-size: 24px;
            font-weight: 600;
        }
        .content {
            padding: 20px;
        }
        .section {
            margin-bottom: 20px;
        }
        .section-title {
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 10px;
            color: #333;
        }
        .toggle-container {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 12px;
            background: #f8f9fa;
            border-radius: 8px;
            margin-bottom: 10px;
        }
        .toggle {
            width: 50px;
            height: 24px;
            background: #ccc;
            border-radius: 12px;
            position: relative;
            cursor: pointer;
            transition: background 0.3s;
        }
        .toggle.active {
            background: #4CAF50;
        }
        .toggle::after {
            content: '';
            position: absolute;
            width: 20px;
            height: 20px;
            background: white;
            border-radius: 50%;
            top: 2px;
            left: 2px;
            transition: transform 0.3s;
        }
        .toggle.active::after {
            transform: translateX(26px);
        }
        .input-group {
            margin-bottom: 15px;
        }
        .input-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: 500;
            color: #555;
        }
        .input-group input {
            width: 100%;
            padding: 12px;
            border: 1px solid #ddd;
            border-radius: 6px;
            font-size: 16px;
            box-sizing: border-box;
        }
        .btn {
            background: #667eea;
            color: white;
            border: none;
            padding: 12px 20px;
            border-radius: 6px;
            font-size: 16px;
            cursor: pointer;
            width: 100%;
            margin-bottom: 10px;
            transition: background 0.3s;
        }
        .btn:hover {
            background: #5a6fd8;
        }
        .btn-secondary {
            background: #6c757d;
        }
        .btn-secondary:hover {
            background: #5a6268;
        }
        .blocked-list {
            max-height: 200px;
            overflow-y: auto;
            border: 1px solid #eee;
            border-radius: 6px;
        }
        .blocked-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px;
            border-bottom: 1px solid #eee;
        }
        .blocked-item:last-child {
            border-bottom: none;
        }
        .unblock-btn {
            background: #dc3545;
            color: white;
            border: none;
            padding: 6px 12px;
            border-radius: 4px;
            font-size: 12px;
            cursor: pointer;
        }
        .status {
            padding: 10px;
            border-radius: 6px;
            margin-bottom: 15px;
            text-align: center;
        }
        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.warning {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        .demo-note {
            background: #e3f2fd;
            color: #1565c0;
            padding: 15px;
            border-radius: 6px;
            margin-bottom: 20px;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🛡️ ScamShield</h1>
            <p style="margin: 5px 0 0 0; opacity: 0.9;">Protect yourself from scam calls & SMS</p>
        </div>
        
        <div class="content">
            <div class="demo-note">
                <strong>Demo Mode:</strong> This is a web demonstration of the ScamShield mobile app interface. The actual app runs on iOS with native call and SMS detection capabilities.
            </div>

            <div class="status success">
                ✅ ScamShield Initialized Successfully
            </div>
            
            <div class="section">
                <div class="section-title">Protection Settings</div>
                
                <div class="toggle-container">
                    <span>Call Protection</span>
                    <div class="toggle active" onclick="toggleProtection(this)"></div>
                </div>
                
                <div class="toggle-container">
                    <span>SMS Protection</span>
                    <div class="toggle active" onclick="toggleProtection(this)"></div>
                </div>
            </div>
            
            <div class="section">
                <div class="section-title">OpenAI API Configuration</div>
                <div class="input-group">
                    <label>API Key</label>
                    <input type="password" placeholder="Enter your OpenAI API key" id="apiKey">
                </div>
                <button class="btn" onclick="setApiKey()">Set API Key</button>
            </div>
            
            <div class="section">
                <div class="section-title">Block a Number</div>
                <div class="input-group">
                    <label>Phone Number</label>
                    <input type="tel" placeholder="+1234567890" id="blockNumber">
                </div>
                <button class="btn" onclick="blockNumber()">Block Number</button>
            </div>
            
            <div class="section">
                <div class="section-title">Blocked Numbers</div>
                <div class="blocked-list" id="blockedList">
                    <div class="blocked-item">
                        <span>******-999-0001</span>
                        <button class="unblock-btn" onclick="unblockNumber(this)">Unblock</button>
                    </div>
                    <div class="blocked-item">
                        <span>******-999-0002</span>
                        <button class="unblock-btn" onclick="unblockNumber(this)">Unblock</button>
                    </div>
                    <div class="blocked-item">
                        <span>******-SCAM-123</span>
                        <button class="unblock-btn" onclick="unblockNumber(this)">Unblock</button>
                    </div>
                </div>
            </div>
            
            <div class="section">
                <button class="btn btn-secondary" onclick="refreshDatabase()">Refresh Scam Database</button>
                <button class="btn btn-secondary" onclick="clearCache()">Clear GPT Cache</button>
            </div>
        </div>
    </div>

    <script>
        function toggleProtection(element) {
            element.classList.toggle('active');
            const isActive = element.classList.contains('active');
            const type = element.parentElement.querySelector('span').textContent;
            console.log(`${type} ${isActive ? 'enabled' : 'disabled'}`);
            showStatus(`${type} ${isActive ? 'enabled' : 'disabled'}`, 'success');
        }

        function setApiKey() {
            const apiKey = document.getElementById('apiKey').value;
            if (apiKey) {
                console.log('API key set:', apiKey.substring(0, 10) + '...');
                showStatus('API key has been set successfully', 'success');
                document.getElementById('apiKey').value = '';
            } else {
                showStatus('Please enter an API key', 'warning');
            }
        }

        function blockNumber() {
            const number = document.getElementById('blockNumber').value;
            if (number) {
                console.log('Blocking number:', number);
                addBlockedNumber(number);
                showStatus(`Number ${number} has been blocked`, 'success');
                document.getElementById('blockNumber').value = '';
            } else {
                showStatus('Please enter a phone number', 'warning');
            }
        }

        function unblockNumber(button) {
            const number = button.parentElement.querySelector('span').textContent;
            console.log('Unblocking number:', number);
            button.parentElement.remove();
            showStatus(`Number ${number} has been unblocked`, 'success');
        }

        function addBlockedNumber(number) {
            const blockedList = document.getElementById('blockedList');
            const item = document.createElement('div');
            item.className = 'blocked-item';
            item.innerHTML = `
                <span>${number}</span>
                <button class="unblock-btn" onclick="unblockNumber(this)">Unblock</button>
            `;
            blockedList.appendChild(item);
        }

        function refreshDatabase() {
            console.log('Refreshing scam database...');
            showStatus('Scam database has been refreshed', 'success');
        }

        function clearCache() {
            console.log('Clearing GPT cache...');
            showStatus('GPT cache has been cleared', 'success');
        }

        function showStatus(message, type) {
            // Remove existing status
            const existingStatus = document.querySelector('.status');
            if (existingStatus) {
                existingStatus.remove();
            }

            // Create new status
            const status = document.createElement('div');
            status.className = `status ${type}`;
            status.textContent = message;
            
            // Insert after demo note
            const demoNote = document.querySelector('.demo-note');
            demoNote.parentNode.insertBefore(status, demoNote.nextSibling);

            // Auto remove after 3 seconds
            setTimeout(() => {
                if (status.parentNode) {
                    status.remove();
                }
            }, 3000);
        }

        // Simulate some activity
        console.log('ScamShield Demo Loaded');
        console.log('Mock: Call detection started');
        console.log('Mock: SMS detection started');
    </script>
</body>
</html>
